#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣账号当日进度爬虫
获取指定用户的当日刷题进度、提交记录等信息
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import os
from dataclasses import dataclass
import csv
import urllib.parse
import re

@dataclass
class SubmissionRecord:
    """提交记录数据类"""
    id: str
    title: str
    title_cn: str
    status: str
    status_display: str
    lang: str
    runtime: str
    memory: str
    timestamp: int
    submission_time: str
    question_id: str
    difficulty: str

class LeetCodeProgressCrawler:
    """力扣进度爬虫"""

    def __init__(self, username: str):
        self.username = username
        self.base_url = "https://leetcode.cn"
        self.graphql_url = "https://leetcode.cn/graphql/"

        # 修复请求头，确保正确的编码处理
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json',
            'Origin': 'https://leetcode.cn',
            'Referer': 'https://leetcode.cn/',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }

        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 今日日期范围
        today = datetime.now()
        self.today_start = int(today.replace(hour=0, minute=0, second=0, microsecond=0).timestamp())
        self.today_end = int(today.replace(hour=23, minute=59, second=59, microsecond=999999).timestamp())

    def get_user_profile(self) -> Dict[str, Any]:
        """获取用户基本信息"""
        query = """
        query userPublicProfile($username: String!) {
            userPublicProfile(username: $username) {
                username
                haveFollowed
                siteRanking
                profile {
                    userSlug
                    realName
                    aboutMe
                    userAvatar
                    location
                    gender
                    websites
                    skillTags
                    contestCount
                    asciiCode
                    ranking
                    skillSet
                }
            }
        }
        """

        variables = {"username": self.username}
        payload = {
            "query": query,
            "variables": variables,
            "operationName": "userPublicProfile"
        }

        try:
            print(f"正在获取用户 {self.username} 的基本信息...")
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=15
            )

            if response.status_code != 200:
                print(f"HTTP请求失败，状态码: {response.status_code}")
                return {}

            data = response.json()

            if 'errors' in data:
                print(f"GraphQL错误: {data['errors']}")
                return {}

            if not data.get('data') or not data['data'].get('userPublicProfile'):
                print(f"用户 {self.username} 不存在或资料不公开")
                return {}

            return data['data']['userPublicProfile']

        except requests.exceptions.RequestException as e:
            print(f"网络请求失败: {e}")
            return {}
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return {}
        except Exception as e:
            print(f"获取用户信息失败: {e}")
            return {}

    def get_user_contest_ranking(self) -> Dict[str, Any]:
        """获取用户竞赛排名信息"""
        query = """
        query userContestRankingInfo($username: String!) {
            userContestRankingInfo(username: $username) {
                attendedContestsCount
                rating
                globalRanking
                totalParticipants
                topPercentage
                badge {
                    name
                }
            }
        }
        """

        variables = {"username": self.username}
        payload = {
            "query": query,
            "variables": variables,
            "operationName": "userContestRankingInfo"
        }

        try:
            print("正在获取竞赛排名信息...")
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=15
            )

            if response.status_code != 200:
                print(f"获取竞赛信息失败，状态码: {response.status_code}")
                return {}

            data = response.json()

            if 'errors' in data:
                print(f"获取竞赛信息时出现错误: {data['errors']}")
                return {}

            return data['data'].get('userContestRankingInfo') or {}

        except Exception as e:
            print(f"获取竞赛排名失败: {e}")
            return {}

    def get_user_solve_count(self) -> Dict[str, Any]:
        """获取用户解题统计"""
        query = """
        query userProblemsSolved($username: String!) {
            allQuestionsCount {
                difficulty
                count
            }
            matchedUser(username: $username) {
                problemsSolvedBeatsStats {
                    difficulty
                    percentage
                }
                submitStatsGlobal {
                    acSubmissionNum {
                        difficulty
                        count
                    }
                }
            }
        }
        """

        variables = {"username": self.username}
        payload = {
            "query": query,
            "variables": variables,
            "operationName": "userProblemsSolved"
        }

        try:
            print("正在获取解题统计...")
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=15
            )

            if response.status_code != 200:
                print(f"获取解题统计失败，状态码: {response.status_code}")
                return {}

            data = response.json()

            if 'errors' in data:
                print(f"获取解题统计时出现错误: {data['errors']}")
                return {}

            return data['data']

        except Exception as e:
            print(f"获取解题统计失败: {e}")
            return {}

    def get_recent_submissions(self, limit: int = 20) -> List[SubmissionRecord]:
        """获取最近的提交记录"""
        query = """
        query recentSubmissions($username: String!, $limit: Int!) {
            recentSubmissions(username: $username, limit: $limit) {
                title
                titleSlug
                timestamp
                statusDisplay
                lang
                __typename
            }
        }
        """

        variables = {"username": self.username, "limit": limit}
        payload = {
            "query": query,
            "variables": variables,
            "operationName": "recentSubmissions"
        }

        try:
            print(f"正在获取最近 {limit} 条提交记录...")
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=15
            )

            if response.status_code != 200:
                print(f"获取提交记录失败，状态码: {response.status_code}")
                return []

            data = response.json()

            if 'errors' in data:
                print(f"获取提交记录时出现错误: {data['errors']}")
                return []

            submissions = []
            recent_submissions = data['data'].get('recentSubmissions') or []

            for sub in recent_submissions:
                try:
                    # 转换时间戳
                    timestamp = int(sub['timestamp'])
                    submission_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

                    record = SubmissionRecord(
                        id=str(timestamp),
                        title=sub['title'],
                        title_cn=sub['title'],  # 这里可能需要额外查询中文标题
                        status=sub['statusDisplay'],
                        status_display=sub['statusDisplay'],
                        lang=sub['lang'],
                        runtime="",
                        memory="",
                        timestamp=timestamp,
                        submission_time=submission_time,
                        question_id=sub['titleSlug'],
                        difficulty=""
                    )
                    submissions.append(record)
                except (KeyError, ValueError) as e:
                    print(f"解析提交记录时出错: {e}")
                    continue

            print(f"成功获取 {len(submissions)} 条提交记录")
            return submissions

        except Exception as e:
            print(f"获取提交记录失败: {e}")
            return []

    def get_today_submissions(self) -> List[SubmissionRecord]:
        """获取今日提交记录"""
        all_submissions = self.get_recent_submissions(100)  # 获取更多记录以确保包含今日的

        today_submissions = []
        for sub in all_submissions:
            if self.today_start <= sub.timestamp <= self.today_end:
                today_submissions.append(sub)

        return today_submissions

    def get_submission_calendar(self) -> Dict[str, Any]:
        """获取提交日历数据"""
        query = """
        query userProfileCalendar($username: String!, $year: Int) {
            matchedUser(username: $username) {
                userCalendar(year: $year) {
                    activeYears
                    streak
                    totalActiveDays
                    dccBadges {
                        timestamp
                        badge {
                            name
                            icon
                        }
                    }
                    submissionCalendar
                }
            }
        }
        """

        current_year = datetime.now().year
        variables = {"username": self.username, "year": current_year}
        payload = {
            "query": query,
            "variables": variables,
            "operationName": "userProfileCalendar"
        }

        try:
            print("正在获取提交日历数据...")
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=15
            )

            if response.status_code != 200:
                print(f"获取提交日历失败，状态码: {response.status_code}")
                return {}

            data = response.json()

            if 'errors' in data:
                print(f"获取提交日历时出现错误: {data['errors']}")
                return {}

            user_data = data['data'].get('matchedUser')
            if not user_data:
                print("用户数据为空")
                return {}

            return user_data.get('userCalendar') or {}

        except Exception as e:
            print(f"获取提交日历失败: {e}")
            return {}

    def analyze_today_progress(self) -> Dict[str, Any]:
        """分析今日进度"""
        print(f"正在分析用户 {self.username} 的今日进度...")
        print("=" * 50)

        # 获取各种数据
        profile = self.get_user_profile()
        contest_info = self.get_user_contest_ranking()
        solve_stats = self.get_user_solve_count()
        today_submissions = self.get_today_submissions()
        calendar_data = self.get_submission_calendar()

        # 检查是否获取到基本数据
        if not profile:
            print("⚠️ 无法获取用户基本信息，可能用户不存在或资料不公开")

        # 分析今日提交
        today_ac_count = 0
        today_total_submissions = len(today_submissions)
        today_languages = set()
        today_problems = set()

        for sub in today_submissions:
            if sub.status in ['Accepted', 'AC', '通过']:
                today_ac_count += 1
            if sub.lang:
                today_languages.add(sub.lang)
            if sub.question_id:
                today_problems.add(sub.question_id)

        # 获取今日提交次数（从日历数据）
        today_str = datetime.now().strftime('%Y-%m-%d')
        today_timestamp = str(int(datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).timestamp()))

        calendar_submissions = 0
        if calendar_data and 'submissionCalendar' in calendar_data:
            try:
                submission_calendar = json.loads(calendar_data['submissionCalendar'])
                calendar_submissions = submission_calendar.get(today_timestamp, 0)
            except (json.JSONDecodeError, TypeError) as e:
                print(f"解析提交日历数据失败: {e}")

        # 构建进度报告
        progress_report = {
            'user_info': {
                'username': self.username,
                'real_name': profile.get('profile', {}).get('realName', '') if profile else '',
                'ranking': profile.get('profile', {}).get('ranking', 0) if profile else 0,
                'location': profile.get('profile', {}).get('location', '') if profile else '',
            },
            'contest_info': contest_info,
            'solve_statistics': solve_stats,
            'today_progress': {
                'date': today_str,
                'total_submissions': today_total_submissions,
                'accepted_submissions': today_ac_count,
                'success_rate': round(today_ac_count / today_total_submissions * 100, 2) if today_total_submissions > 0 else 0,
                'unique_problems': len(today_problems),
                'languages_used': list(today_languages),
                'calendar_submissions': calendar_submissions,
                'submissions_detail': [
                    {
                        'time': sub.submission_time,
                        'problem': sub.title,
                        'status': sub.status_display,
                        'language': sub.lang
                    } for sub in today_submissions
                ]
            },
            'calendar_info': {
                'streak': calendar_data.get('streak', 0) if calendar_data else 0,
                'total_active_days': calendar_data.get('totalActiveDays', 0) if calendar_data else 0,
                'active_years': calendar_data.get('activeYears', []) if calendar_data else []
            },
            'timestamp': datetime.now().isoformat()
        }

        print("数据获取完成！")
        return progress_report

    def save_progress_report(self, progress_report: Dict[str, Any], format_type: str = 'json'):
        """保存进度报告"""
        today_str = datetime.now().strftime('%Y%m%d')

        if format_type == 'json':
            filename = f"leetcode_progress_{self.username}_{today_str}.json"
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(progress_report, f, ensure_ascii=False, indent=2)
                print(f"进度报告已保存到: {filename}")
            except Exception as e:
                print(f"保存JSON文件失败: {e}")

        elif format_type == 'csv':
            filename = f"leetcode_progress_{self.username}_{today_str}.csv"
            try:
                with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.writer(f)

                    # 写入基本信息
                    writer.writerow(['用户信息'])
                    writer.writerow(['用户名', progress_report['user_info']['username']])
                    writer.writerow(['真实姓名', progress_report['user_info']['real_name']])
                    writer.writerow(['排名', progress_report['user_info']['ranking']])
                    writer.writerow(['地区', progress_report['user_info']['location']])
                    writer.writerow([])

                    # 写入今日进度
                    today = progress_report['today_progress']
                    writer.writerow(['今日进度'])
                    writer.writerow(['日期', today['date']])
                    writer.writerow(['总提交次数', today['total_submissions']])
                    writer.writerow(['通过次数', today['accepted_submissions']])
                    writer.writerow(['成功率(%)', today['success_rate']])
                    writer.writerow(['解决问题数', today['unique_problems']])
                    writer.writerow(['使用语言', ', '.join(today['languages_used'])])
                    writer.writerow([])

                    # 写入提交详情
                    writer.writerow(['提交详情'])
                    writer.writerow(['时间', '题目', '状态', '语言'])
                    for sub in today['submissions_detail']:
                        writer.writerow([sub['time'], sub['problem'], sub['status'], sub['language']])

                print(f"进度报告已保存到: {filename}")
            except Exception as e:
                print(f"保存CSV文件失败: {e}")

        elif format_type == 'txt':
            filename = f"leetcode_progress_{self.username}_{today_str}.txt"
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"力扣账号进度报告\n")
                    f.write(f"=" * 50 + "\n\n")

                    # 用户信息
                    user_info = progress_report['user_info']
                    f.write(f"用户信息:\n")
                    f.write(f"  用户名: {user_info['username']}\n")
                    f.write(f"  真实姓名: {user_info['real_name']}\n")
                    f.write(f"  排名: {user_info['ranking']}\n")
                    f.write(f"  地区: {user_info['location']}\n\n")

                    # 今日进度
                    today = progress_report['today_progress']
                    f.write(f"今日进度 ({today['date']}):\n")
                    f.write(f"  总提交次数: {today['total_submissions']}\n")
                    f.write(f"  通过次数: {today['accepted_submissions']}\n")
                    f.write(f"  成功率: {today['success_rate']}%\n")
                    f.write(f"  解决问题数: {today['unique_problems']}\n")
                    f.write(f"  使用语言: {', '.join(today['languages_used'])}\n\n")

                    # 竞赛信息
                    if progress_report['contest_info']:
                        contest = progress_report['contest_info']
                        f.write(f"竞赛信息:\n")
                        f.write(f"  参赛次数: {contest.get('attendedContestsCount', 0)}\n")
                        f.write(f"  当前分数: {contest.get('rating', 0)}\n")
                        f.write(f"  全球排名: {contest.get('globalRanking', 0)}\n")
                        f.write(f"  排名百分比: {contest.get('topPercentage', 0)}%\n\n")

                    # 日历信息
                    calendar = progress_report['calendar_info']
                    f.write(f"刷题统计:\n")
                    f.write(f"  连续刷题天数: {calendar['streak']}\n")
                    f.write(f"  总活跃天数: {calendar['total_active_days']}\n")
                    f.write(f"  活跃年份: {', '.join(map(str, calendar['active_years']))}\n\n")

                    # 提交详情
                    if today['submissions_detail']:
                        f.write(f"今日提交详情:\n")
                        for i, sub in enumerate(today['submissions_detail'], 1):
                            f.write(f"  {i}. {sub['time']} - {sub['problem']} ({sub['status']}) [{sub['language']}]\n")
                    else:
                        f.write(f"今日暂无提交记录\n")

                    f.write(f"\n报告生成时间: {progress_report['timestamp']}\n")

                print(f"进度报告已保存到: {filename}")
            except Exception as e:
                print(f"保存TXT文件失败: {e}")

    def print_progress_summary(self, progress_report: Dict[str, Any]):
        """打印进度摘要"""
        print("\n" + "=" * 60)
        print(f"力扣账号进度报告 - {progress_report['user_info']['username']}")
        print("=" * 60)

        today = progress_report['today_progress']
        print(f"\n📅 今日进度 ({today['date']}):")
        print(f"   📝 总提交次数: {today['total_submissions']}")
        print(f"   ✅ 通过次数: {today['accepted_submissions']}")
        print(f"   📊 成功率: {today['success_rate']}%")
        print(f"   🎯 解决问题数: {today['unique_problems']}")
        print(f"   💻 使用语言: {', '.join(today['languages_used']) if today['languages_used'] else '无'}")

        calendar = progress_report['calendar_info']
        print(f"\n📈 刷题统计:")
        print(f"   🔥 连续刷题: {calendar['streak']} 天")
        print(f"   📅 总活跃天数: {calendar['total_active_days']} 天")

        if progress_report['contest_info']:
            contest = progress_report['contest_info']
            print(f"\n🏆 竞赛信息:")
            print(f"   🎮 参赛次数: {contest.get('attendedContestsCount', 0)}")
            print(f"   ⭐ 当前分数: {contest.get('rating', 0)}")
            print(f"   🌍 全球排名: {contest.get('globalRanking', 0)}")

        if today['submissions_detail']:
            print(f"\n📋 今日提交详情:")
            for i, sub in enumerate(today['submissions_detail'][:10], 1):  # 只显示前10条
                status_emoji = "✅" if sub['status'] in ['Accepted', 'AC', '通过'] else "❌"
                print(f"   {i}. {status_emoji} {sub['time']} - {sub['problem']} [{sub['language']}]")

            if len(today['submissions_detail']) > 10:
                print(f"   ... 还有 {len(today['submissions_detail']) - 10} 条记录")
        else:
            print(f"\n📋 今日暂无提交记录")

        print("\n" + "=" * 60)


def main():
    """主函数"""
    print("力扣账号当日进度爬虫")
    print("=" * 30)

    # 尝试从配置文件读取默认用户名
    try:
        from config import DEFAULT_USERNAME
        default_user = DEFAULT_USERNAME
    except ImportError:
        default_user = ""

    # 获取用户名
    if default_user:
        user_input = input(f"请输入力扣用户名 (默认: {default_user}): ").strip()
        username = user_input if user_input else default_user
    else:
        username = input("请输入力扣用户名: ").strip()

    if not username:
        print("用户名不能为空！")
        return

    try:
        # 创建爬虫实例
        crawler = LeetCodeProgressCrawler(username)

        # 分析今日进度
        progress_report = crawler.analyze_today_progress()

        # 打印进度摘要
        crawler.print_progress_summary(progress_report)

        # 询问是否保存报告
        save_choice = input("\n是否保存进度报告？(y/n): ").strip().lower()
        if save_choice in ['y', 'yes', '是']:
            format_choice = input("选择保存格式 (json/csv/txt/all): ").strip().lower()

            if format_choice == 'all':
                crawler.save_progress_report(progress_report, 'json')
                crawler.save_progress_report(progress_report, 'csv')
                crawler.save_progress_report(progress_report, 'txt')
            elif format_choice in ['json', 'csv', 'txt']:
                crawler.save_progress_report(progress_report, format_choice)
            else:
                print("格式选择无效，默认保存为JSON格式")
                crawler.save_progress_report(progress_report, 'json')

        print("\n程序执行完成！")

    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()