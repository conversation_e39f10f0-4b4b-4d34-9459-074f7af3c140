# 力扣账号当日进度爬虫

这是一个用于爬取力扣（LeetCode中国版）账号当日刷题进度的Python程序。

## 功能特性

- 📊 获取用户基本信息（用户名、排名、地区等）
- 📅 分析当日刷题进度（提交次数、通过率、解决问题数等）
- 🏆 获取竞赛排名信息
- 📈 获取刷题日历统计（连续天数、总活跃天数等）
- 📝 详细的提交记录分析
- 💾 支持多种格式输出（JSON、CSV、TXT）
- 🎨 美观的控制台输出显示

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 基本使用

直接运行主程序：

```bash
python main.py
```

然后按提示输入力扣用户名即可。

### 配置文件

可以修改 `config.py` 文件来自定义程序行为：

- `DEFAULT_USERNAME`: 设置默认用户名
- `OUTPUT_CONFIG`: 配置输出格式和显示选项
- `REQUEST_CONFIG`: 配置请求参数
- `DISPLAY_CONFIG`: 配置显示效果

### 输出格式

程序支持以下输出格式：

1. **JSON格式** (`leetcode_progress_用户名_日期.json`)
   - 包含完整的结构化数据
   - 适合程序处理和数据分析

2. **CSV格式** (`leetcode_progress_用户名_日期.csv`)
   - 表格形式的数据
   - 适合Excel等工具打开

3. **TXT格式** (`leetcode_progress_用户名_日期.txt`)
   - 人类可读的文本报告
   - 适合直接查看和分享

## 输出示例

### 控制台输出
```
============================================================
力扣账号进度报告 - your_username
============================================================

📅 今日进度 (2024-01-15):
   📝 总提交次数: 8
   ✅ 通过次数: 6
   📊 成功率: 75.0%
   🎯 解决问题数: 4
   💻 使用语言: Python, Java

📈 刷题统计:
   🔥 连续刷题: 15 天
   📅 总活跃天数: 120 天

🏆 竞赛信息:
   🎮 参赛次数: 25
   ⭐ 当前分数: 1650
   🌍 全球排名: 12345

📋 今日提交详情:
   1. ✅ 2024-01-15 09:30:15 - 两数之和 [Python]
   2. ❌ 2024-01-15 10:15:22 - 三数之和 [Java]
   3. ✅ 2024-01-15 11:20:33 - 三数之和 [Java]
   ...
============================================================
```

## 数据说明

### 获取的数据包括：

1. **用户基本信息**
   - 用户名、真实姓名
   - 全站排名、地区信息

2. **今日进度统计**
   - 总提交次数和通过次数
   - 成功率计算
   - 解决的不同问题数量
   - 使用的编程语言

3. **竞赛信息**
   - 参赛次数和当前分数
   - 全球排名和百分比

4. **刷题日历**
   - 连续刷题天数
   - 总活跃天数
   - 活跃年份统计

5. **详细提交记录**
   - 每次提交的时间、题目、状态、语言

## 注意事项

1. **用户名必须是公开的**：程序只能获取公开用户的信息
2. **网络连接**：需要稳定的网络连接访问力扣网站
3. **请求频率**：程序会自动控制请求频率，避免对服务器造成压力
4. **数据准确性**：数据来源于力扣官方API，准确性有保障

## 故障排除

### 常见问题

1. **用户不存在或私有**
   - 确认用户名拼写正确
   - 确认用户资料是公开的

2. **网络连接问题**
   - 检查网络连接
   - 尝试使用VPN（如果在海外）

3. **数据获取失败**
   - 稍后重试
   - 检查力扣网站是否正常访问

## 开发说明

程序使用力扣的GraphQL API获取数据，主要包括：

- `userPublicProfile`: 用户公开信息
- `userContestRankingInfo`: 竞赛排名信息
- `userProblemsSolved`: 解题统计
- `recentSubmissions`: 最近提交记录
- `userProfileCalendar`: 提交日历

## 许可证

本项目仅供学习和个人使用，请遵守力扣网站的使用条款。

## 更新日志

- v1.0.0: 初始版本，支持基本的进度获取和多格式输出
