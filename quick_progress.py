#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣当日进度快速查询工具
简化版本，快速获取当日刷题进度
"""

import requests
import json
from datetime import datetime
import sys

class QuickProgressChecker:
    """快速进度检查器"""
    
    def __init__(self, username: str):
        self.username = username
        self.graphql_url = "https://leetcode.cn/graphql/"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        }
        
        # 今日时间范围
        today = datetime.now()
        self.today_start = int(today.replace(hour=0, minute=0, second=0, microsecond=0).timestamp())
        self.today_end = int(today.replace(hour=23, minute=59, second=59, microsecond=999999).timestamp())
    
    def get_today_submissions_count(self):
        """获取今日提交次数（从日历数据）"""
        query = """
        query userProfileCalendar($username: String!, $year: Int) {
            matchedUser(username: $username) {
                userCalendar(year: $year) {
                    streak
                    totalActiveDays
                    submissionCalendar
                }
            }
        }
        """
        
        variables = {"username": self.username, "year": datetime.now().year}
        
        try:
            response = requests.post(
                self.graphql_url,
                json={"query": query, "variables": variables},
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                user_data = data['data']['matchedUser']
                if user_data and user_data['userCalendar']:
                    calendar_data = user_data['userCalendar']
                    submission_calendar = json.loads(calendar_data['submissionCalendar'])
                    
                    # 获取今日提交次数
                    today_timestamp = str(self.today_start)
                    today_submissions = submission_calendar.get(today_timestamp, 0)
                    
                    return {
                        'today_submissions': today_submissions,
                        'streak': calendar_data['streak'],
                        'total_active_days': calendar_data['totalActiveDays']
                    }
            
            return None
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return None
    
    def get_recent_ac_problems(self):
        """获取最近通过的题目"""
        query = """
        query recentSubmissions($username: String!, $limit: Int!) {
            recentSubmissions(username: $username, limit: $limit) {
                title
                titleSlug
                timestamp
                statusDisplay
                lang
            }
        }
        """
        
        variables = {"username": self.username, "limit": 50}
        
        try:
            response = requests.post(
                self.graphql_url,
                json={"query": query, "variables": variables},
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                submissions = data['data']['recentSubmissions'] or []
                
                today_ac = []
                today_total = 0
                
                for sub in submissions:
                    timestamp = int(sub['timestamp'])
                    if self.today_start <= timestamp <= self.today_end:
                        today_total += 1
                        if sub['statusDisplay'] in ['Accepted', 'AC', '通过']:
                            today_ac.append({
                                'title': sub['title'],
                                'time': datetime.fromtimestamp(timestamp).strftime('%H:%M'),
                                'lang': sub['lang']
                            })
                
                return {
                    'today_ac_problems': today_ac,
                    'today_total_submissions': today_total,
                    'today_ac_count': len(today_ac)
                }
            
            return None
            
        except Exception as e:
            print(f"获取提交记录失败: {e}")
            return None
    
    def check_progress(self):
        """检查今日进度"""
        print(f"正在查询 {self.username} 的今日进度...")
        
        # 获取日历数据
        calendar_data = self.get_today_submissions_count()
        
        # 获取提交记录
        submission_data = self.get_recent_ac_problems()
        
        if not calendar_data and not submission_data:
            print("❌ 无法获取数据，请检查用户名是否正确或网络连接")
            return
        
        # 显示结果
        today_str = datetime.now().strftime('%Y-%m-%d')
        print(f"\n📅 {today_str} 进度报告")
        print("=" * 40)
        
        if calendar_data:
            print(f"📊 今日提交: {calendar_data['today_submissions']} 次")
            print(f"🔥 连续天数: {calendar_data['streak']} 天")
            print(f"📈 总活跃: {calendar_data['total_active_days']} 天")
        
        if submission_data:
            ac_count = submission_data['today_ac_count']
            total_count = submission_data['today_total_submissions']
            success_rate = round(ac_count / total_count * 100, 1) if total_count > 0 else 0
            
            print(f"✅ 通过题目: {ac_count} 题")
            print(f"📝 总提交: {total_count} 次")
            print(f"📊 成功率: {success_rate}%")
            
            if submission_data['today_ac_problems']:
                print(f"\n🎯 今日通过的题目:")
                for i, problem in enumerate(submission_data['today_ac_problems'], 1):
                    print(f"  {i}. {problem['time']} - {problem['title']} [{problem['lang']}]")
            else:
                print(f"\n💡 今日还没有通过任何题目，加油！")
        
        print("=" * 40)


def main():
    """主函数"""
    if len(sys.argv) > 1:
        username = sys.argv[1]
    else:
        username = input("请输入力扣用户名: ").strip()
    
    if not username:
        print("用户名不能为空！")
        return
    
    checker = QuickProgressChecker(username)
    checker.check_progress()


if __name__ == "__main__":
    main()
