LeetCode 热题 100 - 分类汇总
==================================================

【哈希】(3题)
------------------------------
 1. 1.两数之和 (简单)
 2. 49.字母异位词分组 (中等)
 3. 128.最长连续序列 (中等)

【双指针】(4题)
------------------------------
 1. 283.移动零 (简单)
 2. 11.盛最多水的容器 (中等)
 3. 15.三数之和 (中等)
 4. 42.接雨水 (困难)

【滑动窗口】(2题)
------------------------------
 1. 3.无重复字符的最长子串 (中等)
 2. 438.找到字符串中所有字母异位词 (中等)

【子串】(3题)
------------------------------
 1. 560.和为 K 的子数组 (中等)
 2. 239.滑动窗口最大值 (困难)
 3. 76.最小覆盖子串 (困难)

【普通数组】(5题)
------------------------------
 1. 53.最大子数组和 (中等)
 2. 56.合并区间 (中等)
 3. 189.轮转数组 (中等)
 4. 238.除自身以外数组的乘积 (中等)
 5. 41.缺失的第一个正数 (困难)

【矩阵】(4题)
------------------------------
 1. 73.矩阵置零 (中等)
 2. 54.螺旋矩阵 (中等)
 3. 48.旋转图像 (中等)
 4. 240.搜索二维矩阵 II (中等)

【链表】(14题)
------------------------------
 1. 160.相交链表 (简单)
 2. 206.反转链表 (简单)
 3. 234.回文链表 (简单)
 4. 141.环形链表 (简单)
 5. 142.环形链表 II (中等)
 6. 21.合并两个有序链表 (简单)
 7. 2.两数相加 (中等)
 8. 19.删除链表的倒数第 N 个结点 (中等)
 9. 24.两两交换链表中的节点 (中等)
10. 25.K 个一组翻转链表 (困难)
11. 138.随机链表的复制 (中等)
12. 148.排序链表 (中等)
13. 23.合并 K 个升序链表 (困难)
14. 146.LRU 缓存 (中等)

【二叉树】(15题)
------------------------------
 1. 94.二叉树的中序遍历 (简单)
 2. 104.二叉树的最大深度 (简单)
 3. 226.翻转二叉树 (简单)
 4. 101.对称二叉树 (简单)
 5. 543.二叉树的直径 (简单)
 6. 102.二叉树的层序遍历 (中等)
 7. 108.将有序数组转换为二叉搜索树 (简单)
 8. 98.验证二叉搜索树 (中等)
 9. 230.二叉搜索树中第 K 小的元素 (中等)
10. 199.二叉树的右视图 (中等)
11. 114.二叉树展开为链表 (中等)
12. 105.从前序与中序遍历序列构造二叉树 (中等)
13. 437.路径总和 III (中等)
14. 236.二叉树的最近公共祖先 (中等)
15. 124.二叉树中的最大路径和 (困难)

【图论】(4题)
------------------------------
 1. 200.岛屿数量 (中等)
 2. 994.腐烂的橘子 (中等)
 3. 207.课程表 (中等)
 4. 208.实现 Trie (前缀树) (中等)

【回溯】(8题)
------------------------------
 1. 46.全排列 (中等)
 2. 78.子集 (中等)
 3. 17.电话号码的字母组合 (中等)
 4. 39.组合总和 (中等)
 5. 22.括号生成 (中等)
 6. 79.单词搜索 (中等)
 7. 131.分割回文串 (中等)
 8. 51.N 皇后 (困难)

【二分查找】(6题)
------------------------------
 1. 35.搜索插入位置 (简单)
 2. 74.搜索二维矩阵 (中等)
 3. 34.在排序数组中查找元素的第一个和最后一个位置 (中等)
 4. 33.搜索旋转排序数组 (中等)
 5. 153.寻找旋转排序数组中的最小值 (中等)
 6. 4.寻找两个正序数组的中位数 (困难)

【栈】(5题)
------------------------------
 1. 20.有效的括号 (简单)
 2. 155.最小栈 (中等)
 3. 394.字符串解码 (中等)
 4. 739.每日温度 (中等)
 5. 84.柱状图中最大的矩形 (困难)

【堆】(3题)
------------------------------
 1. 215.数组中的第K个最大元素 (中等)
 2. 347.前 K 个高频元素 (中等)
 3. 295.数据流的中位数 (困难)

【贪心算法】(4题)
------------------------------
 1. 121.买卖股票的最佳时机 (简单)
 2. 55.跳跃游戏 (中等)
 3. 45.跳跃游戏 II (中等)
 4. 763.划分字母区间 (中等)

【动态规划】(10题)
------------------------------
 1. 70.爬楼梯 (简单)
 2. 118.杨辉三角 (简单)
 3. 198.打家劫舍 (中等)
 4. 279.完全平方数 (中等)
 5. 322.零钱兑换 (中等)
 6. 139.单词拆分 (中等)
 7. 300.最长递增子序列 (中等)
 8. 152.乘积最大子数组 (中等)
 9. 416.分割等和子集 (中等)
10. 32.最长有效括号 (困难)

【多维动态规划】(5题)
------------------------------
 1. 62.不同路径 (中等)
 2. 64.最小路径和 (中等)
 3. 5.最长回文子串 (中等)
 4. 1143.最长公共子序列 (中等)
 5. 72.编辑距离 (中等)

【技巧】(5题)
------------------------------
 1. 136.只出现一次的数字 (简单)
 2. 169.多数元素 (简单)
 3. 75.颜色分类 (中等)
 4. 31.下一个排列 (中等)
 5. 287.寻找重复数 (中等)

