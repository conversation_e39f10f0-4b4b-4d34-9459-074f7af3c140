#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣进度爬虫安装脚本
自动检查和安装依赖，配置环境
"""

import subprocess
import sys
import os

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("❌ 需要Python 3.6或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    else:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True

def install_requirements():
    """安装依赖包"""
    print("\n安装依赖包...")
    
    requirements = ['requests>=2.25.0', 'pandas>=1.3.0']
    
    for package in requirements:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    return True

def create_config_if_not_exists():
    """如果配置文件不存在则创建"""
    config_file = 'config.py'
    
    if not os.path.exists(config_file):
        print(f"\n创建配置文件 {config_file}...")
        
        config_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣进度爬虫配置文件
"""

# 默认用户名（可以在这里设置常用的用户名）
DEFAULT_USERNAME = ""

# 请求配置
REQUEST_CONFIG = {
    'timeout': 30,
    'max_retries': 3,
    'retry_delay': 1,  # 重试间隔（秒）
}

# 输出配置
OUTPUT_CONFIG = {
    'default_format': 'json',  # 默认保存格式: json, csv, txt, all
    'auto_save': False,  # 是否自动保存报告
    'show_detailed_submissions': True,  # 是否显示详细提交记录
    'max_display_submissions': 10,  # 最多显示的提交记录数
}

# 日期配置
DATE_CONFIG = {
    'timezone': 'Asia/Shanghai',  # 时区
    'date_format': '%Y-%m-%d %H:%M:%S',  # 日期格式
}

# 文件名配置
FILENAME_CONFIG = {
    'include_username': True,  # 文件名是否包含用户名
    'include_date': True,  # 文件名是否包含日期
    'date_format': '%Y%m%d',  # 文件名中的日期格式
}

# GraphQL查询配置
GRAPHQL_CONFIG = {
    'recent_submissions_limit': 100,  # 获取最近提交记录的数量
    'enable_cache': False,  # 是否启用缓存
    'cache_duration': 300,  # 缓存持续时间（秒）
}

# 显示配置
DISPLAY_CONFIG = {
    'use_emoji': True,  # 是否使用emoji
    'show_progress_bar': False,  # 是否显示进度条
    'colored_output': True,  # 是否使用彩色输出
}
'''
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            print(f"✅ 配置文件 {config_file} 创建成功")
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    else:
        print(f"✅ 配置文件 {config_file} 已存在")
    
    return True

def create_usernames_file_if_not_exists():
    """如果用户名文件不存在则创建"""
    usernames_file = 'usernames.txt'
    
    if not os.path.exists(usernames_file):
        print(f"\n创建用户名示例文件 {usernames_file}...")
        
        usernames_content = '''# 力扣用户名列表示例
# 每行一个用户名，以#开头的行为注释

# 示例用户名（请替换为实际的用户名）
example-user1
example-user2
example-user3

# 你可以在这里添加更多用户名
# your-username-here
'''
        
        try:
            with open(usernames_file, 'w', encoding='utf-8') as f:
                f.write(usernames_content)
            print(f"✅ 用户名文件 {usernames_file} 创建成功")
        except Exception as e:
            print(f"❌ 创建用户名文件失败: {e}")
            return False
    else:
        print(f"✅ 用户名文件 {usernames_file} 已存在")
    
    return True

def setup_default_username():
    """设置默认用户名"""
    print("\n配置默认用户名...")
    
    username = input("请输入你的力扣用户名（可选，直接回车跳过）: ").strip()
    
    if username:
        try:
            # 读取现有配置
            with open('config.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换默认用户名
            content = content.replace('DEFAULT_USERNAME = ""', f'DEFAULT_USERNAME = "{username}"')
            
            # 写回文件
            with open('config.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 默认用户名设置为: {username}")
        except Exception as e:
            print(f"❌ 设置默认用户名失败: {e}")
            return False
    else:
        print("⏭️ 跳过默认用户名设置")
    
    return True

def test_installation():
    """测试安装"""
    print("\n测试安装...")
    
    try:
        import requests
        import pandas
        print("✅ 依赖包导入成功")
        
        # 测试网络连接
        response = requests.get("https://leetcode.cn", timeout=5)
        if response.status_code == 200:
            print("✅ 网络连接正常")
        else:
            print("⚠️ 网络连接可能有问题")
        
        return True
    except ImportError as e:
        print(f"❌ 依赖包导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ 网络测试失败: {e}")
        return True  # 网络问题不影响安装

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 力扣进度爬虫安装程序")
    print("=" * 60)
    
    success = True
    
    # 检查Python版本
    if not check_python_version():
        success = False
    
    # 安装依赖
    if success and not install_requirements():
        success = False
    
    # 创建配置文件
    if success and not create_config_if_not_exists():
        success = False
    
    # 创建用户名文件
    if success and not create_usernames_file_if_not_exists():
        success = False
    
    # 设置默认用户名
    if success and not setup_default_username():
        success = False
    
    # 测试安装
    if success and not test_installation():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 安装完成！")
        print("\n使用方法:")
        print("1. 运行 python run.py 启动菜单")
        print("2. 运行 python daily_progress.py 查看今日进度")
        print("3. 运行 python main.py 获取完整分析")
        print("\n更多信息请查看 README.md")
    else:
        print("❌ 安装过程中出现错误")
        print("请检查错误信息并手动解决")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
