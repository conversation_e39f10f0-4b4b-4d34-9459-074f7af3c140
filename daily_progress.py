#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣每日进度查询工具
专门用于快速获取当日刷题进度
"""

import requests
import json
from datetime import datetime
import sys

class DailyProgressChecker:
    """每日进度检查器"""
    
    def __init__(self, username: str):
        self.username = username
        self.graphql_url = "https://leetcode.cn/graphql/"
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'Origin': 'https://leetcode.cn',
            'Referer': 'https://leetcode.cn/',
        }
        
        # 今日时间戳
        today = datetime.now()
        self.today_start = int(today.replace(hour=0, minute=0, second=0, microsecond=0).timestamp())
        self.today_end = int(today.replace(hour=23, minute=59, second=59, microsecond=999999).timestamp())
    
    def get_today_progress(self):
        """获取今日进度"""
        print(f"正在查询 {self.username} 的今日进度...")
        
        # 查询1: 获取提交日历数据
        calendar_query = """
        query userProfileCalendar($username: String!, $year: Int) {
            matchedUser(username: $username) {
                userCalendar(year: $year) {
                    streak
                    totalActiveDays
                    submissionCalendar
                }
            }
        }
        """
        
        calendar_payload = {
            "query": calendar_query,
            "variables": {"username": self.username, "year": datetime.now().year}
        }
        
        # 查询2: 获取最近提交记录
        submissions_query = """
        query recentSubmissions($username: String!, $limit: Int!) {
            recentSubmissions(username: $username, limit: $limit) {
                title
                titleSlug
                timestamp
                statusDisplay
                lang
            }
        }
        """
        
        submissions_payload = {
            "query": submissions_query,
            "variables": {"username": self.username, "limit": 50}
        }
        
        try:
            # 获取日历数据
            calendar_response = requests.post(
                self.graphql_url,
                json=calendar_payload,
                headers=self.headers,
                timeout=10
            )
            
            # 获取提交记录
            submissions_response = requests.post(
                self.graphql_url,
                json=submissions_payload,
                headers=self.headers,
                timeout=10
            )
            
            # 解析日历数据
            calendar_data = {}
            if calendar_response.status_code == 200:
                calendar_json = calendar_response.json()
                if calendar_json.get('data') and calendar_json['data'].get('matchedUser'):
                    user_calendar = calendar_json['data']['matchedUser'].get('userCalendar')
                    if user_calendar:
                        calendar_data = user_calendar
            
            # 解析提交记录
            today_submissions = []
            if submissions_response.status_code == 200:
                submissions_json = submissions_response.json()
                if submissions_json.get('data') and submissions_json['data'].get('recentSubmissions'):
                    all_submissions = submissions_json['data']['recentSubmissions']
                    
                    # 筛选今日提交
                    for sub in all_submissions:
                        timestamp = int(sub['timestamp'])
                        if self.today_start <= timestamp <= self.today_end:
                            today_submissions.append({
                                'title': sub['title'],
                                'status': sub['statusDisplay'],
                                'lang': sub['lang'],
                                'time': datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
                            })
            
            # 分析数据
            return self.analyze_progress(calendar_data, today_submissions)
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return None
    
    def analyze_progress(self, calendar_data, today_submissions):
        """分析进度数据"""
        today_str = datetime.now().strftime('%Y-%m-%d')
        
        # 从日历获取今日提交次数
        calendar_submissions = 0
        if calendar_data and 'submissionCalendar' in calendar_data:
            try:
                submission_calendar = json.loads(calendar_data['submissionCalendar'])
                today_timestamp = str(self.today_start)
                calendar_submissions = submission_calendar.get(today_timestamp, 0)
            except:
                pass
        
        # 分析今日提交记录
        ac_count = 0
        languages = set()
        problems = set()
        
        for sub in today_submissions:
            if sub['status'] in ['Accepted', 'AC', '通过']:
                ac_count += 1
            languages.add(sub['lang'])
            problems.add(sub['title'])
        
        # 计算成功率
        total_submissions = len(today_submissions)
        success_rate = round(ac_count / total_submissions * 100, 1) if total_submissions > 0 else 0
        
        # 构建结果
        result = {
            'date': today_str,
            'calendar_submissions': calendar_submissions,
            'detail_submissions': total_submissions,
            'accepted_count': ac_count,
            'success_rate': success_rate,
            'unique_problems': len(problems),
            'languages': list(languages),
            'streak': calendar_data.get('streak', 0),
            'total_active_days': calendar_data.get('totalActiveDays', 0),
            'submissions_detail': today_submissions
        }
        
        return result
    
    def display_progress(self, progress):
        """显示进度"""
        if not progress:
            print("❌ 无法获取进度数据")
            return
        
        print("\n" + "=" * 50)
        print(f"📅 {progress['date']} 刷题进度")
        print("=" * 50)
        
        print(f"📊 今日提交: {progress['calendar_submissions']} 次")
        print(f"✅ 通过题目: {progress['accepted_count']} 题")
        print(f"📈 成功率: {progress['success_rate']}%")
        print(f"🎯 解决问题: {progress['unique_problems']} 个")
        print(f"💻 使用语言: {', '.join(progress['languages']) if progress['languages'] else '无'}")
        print(f"🔥 连续天数: {progress['streak']} 天")
        print(f"📅 总活跃: {progress['total_active_days']} 天")
        
        if progress['submissions_detail']:
            print(f"\n📋 今日提交详情:")
            for i, sub in enumerate(progress['submissions_detail'], 1):
                status_emoji = "✅" if sub['status'] in ['Accepted', 'AC', '通过'] else "❌"
                print(f"  {i}. {status_emoji} {sub['time']} - {sub['title']} [{sub['lang']}]")
        else:
            print(f"\n💡 今日还没有提交记录")
        
        print("=" * 50)
    
    def save_progress(self, progress, filename=None):
        """保存进度到文件"""
        if not progress:
            return
        
        if not filename:
            today_str = datetime.now().strftime('%Y%m%d')
            filename = f"daily_progress_{self.username}_{today_str}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(progress, f, ensure_ascii=False, indent=2)
            print(f"📁 进度已保存到: {filename}")
        except Exception as e:
            print(f"保存失败: {e}")


def main():
    """主函数"""
    # 尝试从配置文件读取默认用户名
    try:
        from config import DEFAULT_USERNAME
        default_user = DEFAULT_USERNAME
    except ImportError:
        default_user = ""
    
    # 获取用户名
    if len(sys.argv) > 1:
        username = sys.argv[1]
    elif default_user:
        user_input = input(f"请输入力扣用户名 (默认: {default_user}): ").strip()
        username = user_input if user_input else default_user
    else:
        username = input("请输入力扣用户名: ").strip()
    
    if not username:
        print("用户名不能为空！")
        return
    
    # 创建检查器并获取进度
    checker = DailyProgressChecker(username)
    progress = checker.get_today_progress()
    
    # 显示进度
    checker.display_progress(progress)
    
    # 询问是否保存
    if progress:
        save_choice = input("\n是否保存进度数据？(y/n): ").strip().lower()
        if save_choice in ['y', 'yes', '是']:
            checker.save_progress(progress)


if __name__ == "__main__":
    main()
