# 力扣进度爬虫使用说明

## 🎯 项目简介

这是一个专门用于获取力扣（LeetCode中国版）账号当日刷题进度的Python工具集。已修复字符编码问题，完美支持中文用户名。

## 📁 文件说明

### 🚀 主要程序
- `daily_progress.py` - **每日进度查询**（推荐日常使用）
- `main.py` - 完整进度分析
- `quick_progress.py` - 快速进度查询
- `batch_progress.py` - 批量用户查询

### 🛠️ 工具程序
- `run.py` - 启动器（提供菜单选择）
- `setup.py` - 自动安装脚本
- `test_api.py` - API连接测试
- `simple_test.py` - 简单API测试

### ⚙️ 配置文件
- `config.py` - 配置文件（可设置默认用户名）
- `requirements.txt` - 依赖包列表
- `usernames.txt` - 批量查询用户名列表

## 🚀 快速开始

### 1. 自动安装（推荐）
```bash
python setup.py
```

### 2. 启动程序
```bash
python run.py
```

### 3. 或直接使用
```bash
# 查看今日进度（最常用）
python daily_progress.py

# 完整分析
python main.py

# 快速查询
python quick_progress.py
```

## 📊 功能对比

| 功能 | daily_progress.py | main.py | quick_progress.py | batch_progress.py |
|------|-------------------|---------|-------------------|-------------------|
| 今日提交次数 | ✅ | ✅ | ✅ | ✅ |
| 通过题目数 | ✅ | ✅ | ✅ | ✅ |
| 成功率计算 | ✅ | ✅ | ✅ | ✅ |
| 连续天数 | ✅ | ✅ | ✅ | ✅ |
| 提交详情 | ✅ | ✅ | ✅ | ✅ |
| 用户基本信息 | ❌ | ✅ | ❌ | ❌ |
| 竞赛排名 | ❌ | ✅ | ❌ | ❌ |
| 解题统计 | ❌ | ✅ | ❌ | ❌ |
| 多格式保存 | JSON | JSON/CSV/TXT | ❌ | CSV |
| 批量查询 | ❌ | ❌ | ❌ | ✅ |
| 启动速度 | 快 | 慢 | 最快 | 中等 |

## 💡 使用建议

### 日常使用
- **推荐**: `daily_progress.py` - 快速查看今日进度
- **命令**: `python daily_progress.py`

### 详细分析
- **推荐**: `main.py` - 获取完整的用户信息和进度分析
- **命令**: `python main.py`

### 批量查询
- **推荐**: `batch_progress.py` - 查询多个用户，生成排行榜
- **命令**: `python batch_progress.py`

### 问题排查
- **推荐**: `test_api.py` - 测试API连接是否正常
- **命令**: `python test_api.py 用户名`

## ⚙️ 配置说明

### 设置默认用户名
编辑 `config.py` 文件：
```python
DEFAULT_USERNAME = "你的用户名"
```

### 批量查询用户列表
编辑 `usernames.txt` 文件：
```
用户名1
用户名2
用户名3
```

## 🔧 常见问题

### Q: 提示用户不存在？
A: 
1. 检查用户名拼写是否正确
2. 确认用户资料是公开的
3. 访问 `https://leetcode.cn/u/用户名/` 确认用户存在

### Q: 网络连接失败？
A:
1. 检查网络连接
2. 确认能正常访问 leetcode.cn
3. 运行 `python test_api.py 用户名` 测试连接

### Q: 字符编码错误？
A: 已修复，程序现在完美支持中文用户名

### Q: 没有今日数据？
A: 
1. 确认今天确实有提交记录
2. 检查时区设置（默认为北京时间）
3. 稍后重试，可能是数据同步延迟

## 📈 输出示例

```
==================================================
📅 2024-01-15 刷题进度
==================================================
📊 今日提交: 8 次
✅ 通过题目: 6 题
📈 成功率: 75.0%
🎯 解决问题: 4 个
💻 使用语言: Python, Java
🔥 连续天数: 15 天
📅 总活跃: 120 天

📋 今日提交详情:
  1. ✅ 09:30:15 - 两数之和 [Python]
  2. ❌ 10:15:22 - 三数之和 [Java]
  3. ✅ 11:20:33 - 三数之和 [Java]
==================================================
```

## 🎉 开始使用

1. 运行安装脚本: `python setup.py`
2. 启动程序: `python run.py`
3. 选择功能，输入用户名
4. 查看进度报告

祝你刷题愉快！🚀
