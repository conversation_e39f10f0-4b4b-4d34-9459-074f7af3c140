#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣账号批量进度查询工具
可以同时查询多个用户的当日进度
"""

import requests
import json
from datetime import datetime
import time
import csv
from typing import List, Dict
import concurrent.futures
import threading

class BatchProgressChecker:
    """批量进度检查器"""
    
    def __init__(self):
        self.graphql_url = "https://leetcode.cn/graphql/"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        }
        
        # 今日时间范围
        today = datetime.now()
        self.today_start = int(today.replace(hour=0, minute=0, second=0, microsecond=0).timestamp())
        self.today_end = int(today.replace(hour=23, minute=59, second=59, microsecond=999999).timestamp())
        
        # 线程锁
        self.lock = threading.Lock()
        self.results = []
    
    def get_user_progress(self, username: str) -> Dict:
        """获取单个用户的进度"""
        try:
            # 获取日历数据
            calendar_query = """
            query userProfileCalendar($username: String!, $year: Int) {
                matchedUser(username: $username) {
                    userCalendar(year: $year) {
                        streak
                        totalActiveDays
                        submissionCalendar
                    }
                }
            }
            """
            
            variables = {"username": username, "year": datetime.now().year}
            
            response = requests.post(
                self.graphql_url,
                json={"query": calendar_query, "variables": variables},
                headers=self.headers,
                timeout=15
            )
            
            today_submissions = 0
            streak = 0
            total_active_days = 0
            
            if response.status_code == 200:
                data = response.json()
                user_data = data['data']['matchedUser']
                if user_data and user_data['userCalendar']:
                    calendar_data = user_data['userCalendar']
                    submission_calendar = json.loads(calendar_data['submissionCalendar'])
                    today_timestamp = str(self.today_start)
                    today_submissions = submission_calendar.get(today_timestamp, 0)
                    streak = calendar_data['streak']
                    total_active_days = calendar_data['totalActiveDays']
            
            # 获取最近提交记录
            submission_query = """
            query recentSubmissions($username: String!, $limit: Int!) {
                recentSubmissions(username: $username, limit: $limit) {
                    title
                    timestamp
                    statusDisplay
                    lang
                }
            }
            """
            
            variables = {"username": username, "limit": 30}
            
            response = requests.post(
                self.graphql_url,
                json={"query": submission_query, "variables": variables},
                headers=self.headers,
                timeout=15
            )
            
            today_ac_count = 0
            today_total_count = 0
            languages = set()
            
            if response.status_code == 200:
                data = response.json()
                submissions = data['data']['recentSubmissions'] or []
                
                for sub in submissions:
                    timestamp = int(sub['timestamp'])
                    if self.today_start <= timestamp <= self.today_end:
                        today_total_count += 1
                        languages.add(sub['lang'])
                        if sub['statusDisplay'] in ['Accepted', 'AC', '通过']:
                            today_ac_count += 1
            
            success_rate = round(today_ac_count / today_total_count * 100, 1) if today_total_count > 0 else 0
            
            return {
                'username': username,
                'today_submissions': today_submissions,
                'today_ac_count': today_ac_count,
                'today_total_count': today_total_count,
                'success_rate': success_rate,
                'streak': streak,
                'total_active_days': total_active_days,
                'languages': list(languages),
                'status': 'success'
            }
            
        except Exception as e:
            return {
                'username': username,
                'status': 'error',
                'error': str(e)
            }
    
    def check_multiple_users(self, usernames: List[str], max_workers: int = 5):
        """批量检查多个用户的进度"""
        print(f"开始批量查询 {len(usernames)} 个用户的进度...")
        print("=" * 50)
        
        # 使用线程池并发查询
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_username = {
                executor.submit(self.get_user_progress, username): username 
                for username in usernames
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_username):
                username = future_to_username[future]
                try:
                    result = future.result()
                    with self.lock:
                        self.results.append(result)
                    
                    # 实时显示进度
                    if result['status'] == 'success':
                        print(f"✅ {username}: {result['today_ac_count']}题通过, {result['streak']}天连续")
                    else:
                        print(f"❌ {username}: 查询失败 - {result.get('error', '未知错误')}")
                        
                except Exception as e:
                    print(f"❌ {username}: 处理结果时出错 - {e}")
                
                # 添加延迟避免请求过快
                time.sleep(0.5)
        
        return self.results
    
    def save_results_to_csv(self, filename: str = None):
        """保存结果到CSV文件"""
        if not filename:
            today_str = datetime.now().strftime('%Y%m%d')
            filename = f"leetcode_batch_progress_{today_str}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                
                # 写入表头
                writer.writerow([
                    '用户名', '今日提交', '今日通过', '总提交', '成功率(%)', 
                    '连续天数', '总活跃天数', '使用语言', '状态'
                ])
                
                # 写入数据
                for result in self.results:
                    if result['status'] == 'success':
                        writer.writerow([
                            result['username'],
                            result['today_submissions'],
                            result['today_ac_count'],
                            result['today_total_count'],
                            result['success_rate'],
                            result['streak'],
                            result['total_active_days'],
                            ', '.join(result['languages']),
                            '成功'
                        ])
                    else:
                        writer.writerow([
                            result['username'], '', '', '', '', '', '', '', 
                            f"失败: {result.get('error', '未知错误')}"
                        ])
            
            print(f"\n📊 结果已保存到: {filename}")
            
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
    
    def print_summary(self):
        """打印汇总信息"""
        if not self.results:
            return
        
        successful_results = [r for r in self.results if r['status'] == 'success']
        failed_count = len(self.results) - len(successful_results)
        
        print(f"\n📈 批量查询汇总")
        print("=" * 30)
        print(f"总查询用户: {len(self.results)}")
        print(f"成功查询: {len(successful_results)}")
        print(f"失败查询: {failed_count}")
        
        if successful_results:
            # 统计信息
            total_ac = sum(r['today_ac_count'] for r in successful_results)
            total_submissions = sum(r['today_submissions'] for r in successful_results)
            avg_streak = sum(r['streak'] for r in successful_results) / len(successful_results)
            
            print(f"\n🎯 今日统计:")
            print(f"总通过题目: {total_ac}")
            print(f"总提交次数: {total_submissions}")
            print(f"平均连续天数: {avg_streak:.1f}")
            
            # 排行榜
            print(f"\n🏆 今日通过题目排行:")
            sorted_results = sorted(successful_results, key=lambda x: x['today_ac_count'], reverse=True)
            for i, result in enumerate(sorted_results[:10], 1):
                print(f"  {i}. {result['username']}: {result['today_ac_count']}题")
            
            print(f"\n🔥 连续刷题天数排行:")
            sorted_by_streak = sorted(successful_results, key=lambda x: x['streak'], reverse=True)
            for i, result in enumerate(sorted_by_streak[:10], 1):
                print(f"  {i}. {result['username']}: {result['streak']}天")


def load_usernames_from_file(filename: str) -> List[str]:
    """从文件加载用户名列表"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            usernames = [line.strip() for line in f if line.strip()]
        return usernames
    except Exception as e:
        print(f"读取文件失败: {e}")
        return []


def main():
    """主函数"""
    print("力扣账号批量进度查询工具")
    print("=" * 30)
    
    # 获取用户名列表
    choice = input("选择输入方式 (1-手动输入 2-从文件读取): ").strip()
    
    usernames = []
    
    if choice == '1':
        print("请输入用户名，每行一个，输入空行结束:")
        while True:
            username = input().strip()
            if not username:
                break
            usernames.append(username)
    
    elif choice == '2':
        filename = input("请输入文件名 (默认: usernames.txt): ").strip()
        if not filename:
            filename = "usernames.txt"
        usernames = load_usernames_from_file(filename)
    
    else:
        print("无效选择")
        return
    
    if not usernames:
        print("没有找到用户名")
        return
    
    print(f"将查询 {len(usernames)} 个用户: {', '.join(usernames)}")
    
    # 开始批量查询
    checker = BatchProgressChecker()
    results = checker.check_multiple_users(usernames)
    
    # 显示汇总
    checker.print_summary()
    
    # 询问是否保存
    save_choice = input("\n是否保存结果到CSV文件? (y/n): ").strip().lower()
    if save_choice in ['y', 'yes', '是']:
        checker.save_results_to_csv()
    
    print("\n查询完成！")


if __name__ == "__main__":
    main()
