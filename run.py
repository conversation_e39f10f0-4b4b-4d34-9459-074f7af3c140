#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣进度爬虫启动器
提供菜单选择不同的功能
"""

import sys
import os

def show_menu():
    """显示菜单"""
    print("=" * 50)
    print("🚀 力扣账号进度爬虫工具")
    print("=" * 50)
    print("请选择功能:")
    print("1. 📅 每日进度查询 (推荐)")
    print("2. 📊 完整进度分析")
    print("3. ⚡ 快速进度查询")
    print("4. 👥 批量用户查询")
    print("5. 🔧 API连接测试")
    print("6. 📖 查看帮助")
    print("0. 退出")
    print("=" * 50)

def run_daily_progress():
    """运行每日进度查询"""
    print("启动每日进度查询...")
    os.system("python daily_progress.py")

def run_full_analysis():
    """运行完整进度分析"""
    print("启动完整进度分析...")
    os.system("python main.py")

def run_quick_progress():
    """运行快速进度查询"""
    print("启动快速进度查询...")
    os.system("python quick_progress.py")

def run_batch_progress():
    """运行批量用户查询"""
    print("启动批量用户查询...")
    os.system("python batch_progress.py")

def run_api_test():
    """运行API测试"""
    print("启动API连接测试...")
    
    # 尝试从配置文件读取默认用户名
    try:
        from config import DEFAULT_USERNAME
        default_user = DEFAULT_USERNAME
    except ImportError:
        default_user = ""
    
    if default_user:
        user_input = input(f"请输入测试用户名 (默认: {default_user}): ").strip()
        username = user_input if user_input else default_user
    else:
        username = input("请输入测试用户名: ").strip()
    
    if username:
        os.system(f"python test_api.py {username}")
    else:
        print("用户名不能为空！")

def show_help():
    """显示帮助信息"""
    print("\n" + "=" * 60)
    print("📖 力扣进度爬虫工具帮助")
    print("=" * 60)
    print()
    print("🎯 功能说明:")
    print("1. 每日进度查询 - 快速查看当日刷题进度，推荐日常使用")
    print("2. 完整进度分析 - 获取详细的用户信息和全面分析")
    print("3. 快速进度查询 - 轻量级版本，获取基本进度信息")
    print("4. 批量用户查询 - 同时查询多个用户的进度，支持排行榜")
    print("5. API连接测试 - 测试与力扣服务器的连接是否正常")
    print()
    print("⚙️ 配置说明:")
    print("- 编辑 config.py 文件可以设置默认用户名")
    print("- 支持中文用户名，已修复字符编码问题")
    print("- 程序会自动处理网络请求和数据解析")
    print()
    print("📁 输出文件:")
    print("- JSON格式: 结构化数据，适合程序处理")
    print("- CSV格式: 表格数据，适合Excel分析")
    print("- TXT格式: 文本报告，适合直接查看")
    print()
    print("🔧 故障排除:")
    print("- 确保用户名正确且资料公开")
    print("- 检查网络连接是否正常")
    print("- 使用API测试功能检查连接状态")
    print("- 查看 README.md 获取详细说明")
    print()
    print("=" * 60)
    input("按回车键返回主菜单...")

def main():
    """主函数"""
    while True:
        try:
            show_menu()
            choice = input("请输入选项 (0-6): ").strip()
            
            if choice == '0':
                print("👋 感谢使用，再见！")
                break
            elif choice == '1':
                run_daily_progress()
            elif choice == '2':
                run_full_analysis()
            elif choice == '3':
                run_quick_progress()
            elif choice == '4':
                run_batch_progress()
            elif choice == '5':
                run_api_test()
            elif choice == '6':
                show_help()
            else:
                print("❌ 无效选项，请重新选择")
            
            if choice != '0' and choice != '6':
                input("\n按回车键返回主菜单...")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
