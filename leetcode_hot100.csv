﻿序号,题号,题目(中文),题目(英文),难度,分类,标签,是否付费,链接
1,1,两数之和,Two Sum,简单,哈希,"数组, 哈希表",否,https://leetcode.cn/problems/two-sum/
2,49,字母异位词分组,Group Anagrams,中等,哈希,"数组, 哈希表, 字符串, 排序",否,https://leetcode.cn/problems/group-anagrams/
3,128,最长连续序列,Longest Consecutive Sequence,中等,哈希,"并查集, 数组, 哈希表",否,https://leetcode.cn/problems/longest-consecutive-sequence/
4,283,移动零,Move Zeroes,简单,双指针,"数组, 双指针",否,https://leetcode.cn/problems/move-zeroes/
5,11,盛最多水的容器,Container With Most Water,中等,双指针,"贪心, 数组, 双指针",否,https://leetcode.cn/problems/container-with-most-water/
6,15,三数之和,3Sum,中等,双指针,"数组, 双指针, 排序",否,https://leetcode.cn/problems/3sum/
7,42,接雨水,Trapping Rain Water,困难,双指针,"栈, 数组, 双指针, 动态规划, 单调栈",否,https://leetcode.cn/problems/trapping-rain-water/
8,3,无重复字符的最长子串,Longest Substring Without Repeating Characters,中等,滑动窗口,"哈希表, 字符串, 滑动窗口",否,https://leetcode.cn/problems/longest-substring-without-repeating-characters/
9,438,找到字符串中所有字母异位词,Find All Anagrams in a String,中等,滑动窗口,"哈希表, 字符串, 滑动窗口",否,https://leetcode.cn/problems/find-all-anagrams-in-a-string/
10,560,和为 K 的子数组,Subarray Sum Equals K,中等,子串,"数组, 哈希表, 前缀和",否,https://leetcode.cn/problems/subarray-sum-equals-k/
11,239,滑动窗口最大值,Sliding Window Maximum,困难,子串,"队列, 数组, 滑动窗口, 单调队列, 堆（优先队列）",否,https://leetcode.cn/problems/sliding-window-maximum/
12,76,最小覆盖子串,Minimum Window Substring,困难,子串,"哈希表, 字符串, 滑动窗口",否,https://leetcode.cn/problems/minimum-window-substring/
13,53,最大子数组和,Maximum Subarray,中等,普通数组,"数组, 分治, 动态规划",否,https://leetcode.cn/problems/maximum-subarray/
14,56,合并区间,Merge Intervals,中等,普通数组,"数组, 排序",否,https://leetcode.cn/problems/merge-intervals/
15,189,轮转数组,Rotate Array,中等,普通数组,"数组, 数学, 双指针",否,https://leetcode.cn/problems/rotate-array/
16,238,除自身以外数组的乘积,Product of Array Except Self,中等,普通数组,"数组, 前缀和",否,https://leetcode.cn/problems/product-of-array-except-self/
17,41,缺失的第一个正数,First Missing Positive,困难,普通数组,"数组, 哈希表",否,https://leetcode.cn/problems/first-missing-positive/
18,73,矩阵置零,Set Matrix Zeroes,中等,矩阵,"数组, 哈希表, 矩阵",否,https://leetcode.cn/problems/set-matrix-zeroes/
19,54,螺旋矩阵,Spiral Matrix,中等,矩阵,"数组, 矩阵, 模拟",否,https://leetcode.cn/problems/spiral-matrix/
20,48,旋转图像,Rotate Image,中等,矩阵,"数组, 数学, 矩阵",否,https://leetcode.cn/problems/rotate-image/
21,240,搜索二维矩阵 II,Search a 2D Matrix II,中等,矩阵,"数组, 二分查找, 分治, 矩阵",否,https://leetcode.cn/problems/search-a-2d-matrix-ii/
22,160,相交链表,Intersection of Two Linked Lists,简单,链表,"哈希表, 链表, 双指针",否,https://leetcode.cn/problems/intersection-of-two-linked-lists/
23,206,反转链表,Reverse Linked List,简单,链表,"递归, 链表",否,https://leetcode.cn/problems/reverse-linked-list/
24,234,回文链表,Palindrome Linked List,简单,链表,"栈, 递归, 链表, 双指针",否,https://leetcode.cn/problems/palindrome-linked-list/
25,141,环形链表,Linked List Cycle,简单,链表,"哈希表, 链表, 双指针",否,https://leetcode.cn/problems/linked-list-cycle/
26,142,环形链表 II,Linked List Cycle II,中等,链表,"哈希表, 链表, 双指针",否,https://leetcode.cn/problems/linked-list-cycle-ii/
27,21,合并两个有序链表,Merge Two Sorted Lists,简单,链表,"递归, 链表",否,https://leetcode.cn/problems/merge-two-sorted-lists/
28,2,两数相加,Add Two Numbers,中等,链表,"递归, 链表, 数学",否,https://leetcode.cn/problems/add-two-numbers/
29,19,删除链表的倒数第 N 个结点,Remove Nth Node From End of List,中等,链表,"链表, 双指针",否,https://leetcode.cn/problems/remove-nth-node-from-end-of-list/
30,24,两两交换链表中的节点,Swap Nodes in Pairs,中等,链表,"递归, 链表",否,https://leetcode.cn/problems/swap-nodes-in-pairs/
31,25,K 个一组翻转链表,Reverse Nodes in k-Group,困难,链表,"递归, 链表",否,https://leetcode.cn/problems/reverse-nodes-in-k-group/
32,138,随机链表的复制,Copy List with Random Pointer,中等,链表,"哈希表, 链表",否,https://leetcode.cn/problems/copy-list-with-random-pointer/
33,148,排序链表,Sort List,中等,链表,"链表, 双指针, 分治, 排序, 归并排序",否,https://leetcode.cn/problems/sort-list/
34,23,合并 K 个升序链表,Merge k Sorted Lists,困难,链表,"链表, 分治, 堆（优先队列）, 归并排序",否,https://leetcode.cn/problems/merge-k-sorted-lists/
35,146,LRU 缓存,LRU Cache,中等,链表,"设计, 哈希表, 链表, 双向链表",否,https://leetcode.cn/problems/lru-cache/
36,94,二叉树的中序遍历,Binary Tree Inorder Traversal,简单,二叉树,"栈, 树, 深度优先搜索, 二叉树",否,https://leetcode.cn/problems/binary-tree-inorder-traversal/
37,104,二叉树的最大深度,Maximum Depth of Binary Tree,简单,二叉树,"树, 深度优先搜索, 广度优先搜索, 二叉树",否,https://leetcode.cn/problems/maximum-depth-of-binary-tree/
38,226,翻转二叉树,Invert Binary Tree,简单,二叉树,"树, 深度优先搜索, 广度优先搜索, 二叉树",否,https://leetcode.cn/problems/invert-binary-tree/
39,101,对称二叉树,Symmetric Tree,简单,二叉树,"树, 深度优先搜索, 广度优先搜索, 二叉树",否,https://leetcode.cn/problems/symmetric-tree/
40,543,二叉树的直径,Diameter of Binary Tree,简单,二叉树,"树, 深度优先搜索, 二叉树",否,https://leetcode.cn/problems/diameter-of-binary-tree/
41,102,二叉树的层序遍历,Binary Tree Level Order Traversal,中等,二叉树,"树, 广度优先搜索, 二叉树",否,https://leetcode.cn/problems/binary-tree-level-order-traversal/
42,108,将有序数组转换为二叉搜索树,Convert Sorted Array to Binary Search Tree,简单,二叉树,"树, 二叉搜索树, 数组, 分治, 二叉树",否,https://leetcode.cn/problems/convert-sorted-array-to-binary-search-tree/
43,98,验证二叉搜索树,Validate Binary Search Tree,中等,二叉树,"树, 深度优先搜索, 二叉搜索树, 二叉树",否,https://leetcode.cn/problems/validate-binary-search-tree/
44,230,二叉搜索树中第 K 小的元素,Kth Smallest Element in a BST,中等,二叉树,"树, 深度优先搜索, 二叉搜索树, 二叉树",否,https://leetcode.cn/problems/kth-smallest-element-in-a-bst/
45,199,二叉树的右视图,Binary Tree Right Side View,中等,二叉树,"树, 深度优先搜索, 广度优先搜索, 二叉树",否,https://leetcode.cn/problems/binary-tree-right-side-view/
46,114,二叉树展开为链表,Flatten Binary Tree to Linked List,中等,二叉树,"栈, 树, 深度优先搜索, 链表, 二叉树",否,https://leetcode.cn/problems/flatten-binary-tree-to-linked-list/
47,105,从前序与中序遍历序列构造二叉树,Construct Binary Tree from Preorder and Inorder Traversal,中等,二叉树,"树, 数组, 哈希表, 分治, 二叉树",否,https://leetcode.cn/problems/construct-binary-tree-from-preorder-and-inorder-traversal/
48,437,路径总和 III,Path Sum III,中等,二叉树,"树, 深度优先搜索, 二叉树",否,https://leetcode.cn/problems/path-sum-iii/
49,236,二叉树的最近公共祖先,Lowest Common Ancestor of a Binary Tree,中等,二叉树,"树, 深度优先搜索, 二叉树",否,https://leetcode.cn/problems/lowest-common-ancestor-of-a-binary-tree/
50,124,二叉树中的最大路径和,Binary Tree Maximum Path Sum,困难,二叉树,"树, 深度优先搜索, 动态规划, 二叉树",否,https://leetcode.cn/problems/binary-tree-maximum-path-sum/
51,200,岛屿数量,Number of Islands,中等,图论,"深度优先搜索, 广度优先搜索, 并查集, 数组, 矩阵",否,https://leetcode.cn/problems/number-of-islands/
52,994,腐烂的橘子,Rotting Oranges,中等,图论,"广度优先搜索, 数组, 矩阵",否,https://leetcode.cn/problems/rotting-oranges/
53,207,课程表,Course Schedule,中等,图论,"深度优先搜索, 广度优先搜索, 图, 拓扑排序",否,https://leetcode.cn/problems/course-schedule/
54,208,实现 Trie (前缀树),Implement Trie (Prefix Tree),中等,图论,"设计, 字典树, 哈希表, 字符串",否,https://leetcode.cn/problems/implement-trie-prefix-tree/
55,46,全排列,Permutations,中等,回溯,"数组, 回溯",否,https://leetcode.cn/problems/permutations/
56,78,子集,Subsets,中等,回溯,"位运算, 数组, 回溯",否,https://leetcode.cn/problems/subsets/
57,17,电话号码的字母组合,Letter Combinations of a Phone Number,中等,回溯,"哈希表, 字符串, 回溯",否,https://leetcode.cn/problems/letter-combinations-of-a-phone-number/
58,39,组合总和,Combination Sum,中等,回溯,"数组, 回溯",否,https://leetcode.cn/problems/combination-sum/
59,22,括号生成,Generate Parentheses,中等,回溯,"字符串, 动态规划, 回溯",否,https://leetcode.cn/problems/generate-parentheses/
60,79,单词搜索,Word Search,中等,回溯,"深度优先搜索, 数组, 字符串, 回溯, 矩阵",否,https://leetcode.cn/problems/word-search/
61,131,分割回文串,Palindrome Partitioning,中等,回溯,"字符串, 动态规划, 回溯",否,https://leetcode.cn/problems/palindrome-partitioning/
62,51,N 皇后,N-Queens,困难,回溯,"数组, 回溯",否,https://leetcode.cn/problems/n-queens/
63,35,搜索插入位置,Search Insert Position,简单,二分查找,"数组, 二分查找",否,https://leetcode.cn/problems/search-insert-position/
64,74,搜索二维矩阵,Search a 2D Matrix,中等,二分查找,"数组, 二分查找, 矩阵",否,https://leetcode.cn/problems/search-a-2d-matrix/
65,34,在排序数组中查找元素的第一个和最后一个位置,Find First and Last Position of Element in Sorted Array,中等,二分查找,"数组, 二分查找",否,https://leetcode.cn/problems/find-first-and-last-position-of-element-in-sorted-array/
66,33,搜索旋转排序数组,Search in Rotated Sorted Array,中等,二分查找,"数组, 二分查找",否,https://leetcode.cn/problems/search-in-rotated-sorted-array/
67,153,寻找旋转排序数组中的最小值,Find Minimum in Rotated Sorted Array,中等,二分查找,"数组, 二分查找",否,https://leetcode.cn/problems/find-minimum-in-rotated-sorted-array/
68,4,寻找两个正序数组的中位数,Median of Two Sorted Arrays,困难,二分查找,"数组, 二分查找, 分治",否,https://leetcode.cn/problems/median-of-two-sorted-arrays/
69,20,有效的括号,Valid Parentheses,简单,栈,"栈, 字符串",否,https://leetcode.cn/problems/valid-parentheses/
70,155,最小栈,Min Stack,中等,栈,"栈, 设计",否,https://leetcode.cn/problems/min-stack/
71,394,字符串解码,Decode String,中等,栈,"栈, 递归, 字符串",否,https://leetcode.cn/problems/decode-string/
72,739,每日温度,Daily Temperatures,中等,栈,"栈, 数组, 单调栈",否,https://leetcode.cn/problems/daily-temperatures/
73,84,柱状图中最大的矩形,Largest Rectangle in Histogram,困难,栈,"栈, 数组, 单调栈",否,https://leetcode.cn/problems/largest-rectangle-in-histogram/
74,215,数组中的第K个最大元素,Kth Largest Element in an Array,中等,堆,"数组, 分治, 快速选择, 排序, 堆（优先队列）",否,https://leetcode.cn/problems/kth-largest-element-in-an-array/
75,347,前 K 个高频元素,Top K Frequent Elements,中等,堆,"数组, 哈希表, 分治, 桶排序, 计数, 快速选择, 排序, 堆（优先队列）",否,https://leetcode.cn/problems/top-k-frequent-elements/
76,295,数据流的中位数,Find Median from Data Stream,困难,堆,"设计, 双指针, 数据流, 排序, 堆（优先队列）",否,https://leetcode.cn/problems/find-median-from-data-stream/
77,121,买卖股票的最佳时机,Best Time to Buy and Sell Stock,简单,贪心算法,"数组, 动态规划",否,https://leetcode.cn/problems/best-time-to-buy-and-sell-stock/
78,55,跳跃游戏,Jump Game,中等,贪心算法,"贪心, 数组, 动态规划",否,https://leetcode.cn/problems/jump-game/
79,45,跳跃游戏 II,Jump Game II,中等,贪心算法,"贪心, 数组, 动态规划",否,https://leetcode.cn/problems/jump-game-ii/
80,763,划分字母区间,Partition Labels,中等,贪心算法,"贪心, 哈希表, 双指针, 字符串",否,https://leetcode.cn/problems/partition-labels/
81,70,爬楼梯,Climbing Stairs,简单,动态规划,"记忆化搜索, 数学, 动态规划",否,https://leetcode.cn/problems/climbing-stairs/
82,118,杨辉三角,Pascal's Triangle,简单,动态规划,"数组, 动态规划",否,https://leetcode.cn/problems/pascals-triangle/
83,198,打家劫舍,House Robber,中等,动态规划,"数组, 动态规划",否,https://leetcode.cn/problems/house-robber/
84,279,完全平方数,Perfect Squares,中等,动态规划,"广度优先搜索, 数学, 动态规划",否,https://leetcode.cn/problems/perfect-squares/
85,322,零钱兑换,Coin Change,中等,动态规划,"广度优先搜索, 数组, 动态规划",否,https://leetcode.cn/problems/coin-change/
86,139,单词拆分,Word Break,中等,动态规划,"字典树, 记忆化搜索, 数组, 哈希表, 字符串, 动态规划",否,https://leetcode.cn/problems/word-break/
87,300,最长递增子序列,Longest Increasing Subsequence,中等,动态规划,"数组, 二分查找, 动态规划",否,https://leetcode.cn/problems/longest-increasing-subsequence/
88,152,乘积最大子数组,Maximum Product Subarray,中等,动态规划,"数组, 动态规划",否,https://leetcode.cn/problems/maximum-product-subarray/
89,416,分割等和子集,Partition Equal Subset Sum,中等,动态规划,"数组, 动态规划",否,https://leetcode.cn/problems/partition-equal-subset-sum/
90,32,最长有效括号,Longest Valid Parentheses,困难,动态规划,"栈, 字符串, 动态规划",否,https://leetcode.cn/problems/longest-valid-parentheses/
91,62,不同路径,Unique Paths,中等,多维动态规划,"数学, 动态规划, 组合数学",否,https://leetcode.cn/problems/unique-paths/
92,64,最小路径和,Minimum Path Sum,中等,多维动态规划,"数组, 动态规划, 矩阵",否,https://leetcode.cn/problems/minimum-path-sum/
93,5,最长回文子串,Longest Palindromic Substring,中等,多维动态规划,"双指针, 字符串, 动态规划",否,https://leetcode.cn/problems/longest-palindromic-substring/
94,1143,最长公共子序列,Longest Common Subsequence,中等,多维动态规划,"字符串, 动态规划",否,https://leetcode.cn/problems/longest-common-subsequence/
95,72,编辑距离,Edit Distance,中等,多维动态规划,"字符串, 动态规划",否,https://leetcode.cn/problems/edit-distance/
96,136,只出现一次的数字,Single Number,简单,技巧,"位运算, 数组",否,https://leetcode.cn/problems/single-number/
97,169,多数元素,Majority Element,简单,技巧,"数组, 哈希表, 分治, 计数, 排序",否,https://leetcode.cn/problems/majority-element/
98,75,颜色分类,Sort Colors,中等,技巧,"数组, 双指针, 排序",否,https://leetcode.cn/problems/sort-colors/
99,31,下一个排列,Next Permutation,中等,技巧,"数组, 双指针",否,https://leetcode.cn/problems/next-permutation/
100,287,寻找重复数,Find the Duplicate Number,中等,技巧,"位运算, 数组, 双指针, 二分查找",否,https://leetcode.cn/problems/find-the-duplicate-number/
