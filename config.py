#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣进度爬虫配置文件
"""

# 默认用户名（可以在这里设置常用的用户名）
DEFAULT_USERNAME = "一起消失"

# 请求配置
REQUEST_CONFIG = {
    'timeout': 30,
    'max_retries': 3,
    'retry_delay': 1,  # 重试间隔（秒）
}

# 输出配置
OUTPUT_CONFIG = {
    'default_format': 'json',  # 默认保存格式: json, csv, txt, all
    'auto_save': False,  # 是否自动保存报告
    'show_detailed_submissions': True,  # 是否显示详细提交记录
    'max_display_submissions': 10,  # 最多显示的提交记录数
}

# 日期配置
DATE_CONFIG = {
    'timezone': 'Asia/Shanghai',  # 时区
    'date_format': '%Y-%m-%d %H:%M:%S',  # 日期格式
}

# 文件名配置
FILENAME_CONFIG = {
    'include_username': True,  # 文件名是否包含用户名
    'include_date': True,  # 文件名是否包含日期
    'date_format': '%Y%m%d',  # 文件名中的日期格式
}

# GraphQL查询配置
GRAPHQL_CONFIG = {
    'recent_submissions_limit': 100,  # 获取最近提交记录的数量
    'enable_cache': False,  # 是否启用缓存
    'cache_duration': 300,  # 缓存持续时间（秒）
}

# 显示配置
DISPLAY_CONFIG = {
    'use_emoji': True,  # 是否使用emoji
    'show_progress_bar': False,  # 是否显示进度条
    'colored_output': True,  # 是否使用彩色输出
}
