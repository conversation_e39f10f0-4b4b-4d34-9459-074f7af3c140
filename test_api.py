#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
力扣API测试工具
用于测试和验证力扣GraphQL API的调用
"""

import requests
import json
from datetime import datetime
import sys

class LeetCodeAPITester:
    """力扣API测试器"""
    
    def __init__(self):
        self.base_url = "https://leetcode.cn"
        self.graphql_url = "https://leetcode.cn/graphql/"
        
        # 修复请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json',
            'Origin': 'https://leetcode.cn',
            'Referer': 'https://leetcode.cn/',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def test_user_profile(self, username: str):
        """测试获取用户基本信息"""
        print(f"测试获取用户 {username} 的基本信息...")
        
        query = """
        query userPublicProfile($username: String!) {
            userPublicProfile(username: $username) {
                username
                haveFollowed
                siteRanking
                profile {
                    userSlug
                    realName
                    aboutMe
                    userAvatar
                    location
                    gender
                    websites
                    skillTags
                    contestCount
                    asciiCode
                    ranking
                    skillSet
                }
            }
        }
        """
        
        variables = {"username": username}
        payload = {
            "query": query,
            "variables": variables,
            "operationName": "userPublicProfile"
        }
        
        try:
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=15
            )
            
            print(f"HTTP状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print("响应数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                
                if 'errors' in data:
                    print(f"❌ GraphQL错误: {data['errors']}")
                    return False
                
                if data.get('data') and data['data'].get('userPublicProfile'):
                    print("✅ 用户信息获取成功")
                    return True
                else:
                    print("❌ 用户不存在或资料不公开")
                    return False
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_recent_submissions(self, username: str, limit: int = 10):
        """测试获取最近提交记录"""
        print(f"\n测试获取用户 {username} 的最近 {limit} 条提交记录...")
        
        query = """
        query recentSubmissions($username: String!, $limit: Int!) {
            recentSubmissions(username: $username, limit: $limit) {
                title
                titleSlug
                timestamp
                statusDisplay
                lang
                __typename
            }
        }
        """
        
        variables = {"username": username, "limit": limit}
        payload = {
            "query": query,
            "variables": variables,
            "operationName": "recentSubmissions"
        }
        
        try:
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=15
            )
            
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("响应数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                
                if 'errors' in data:
                    print(f"❌ GraphQL错误: {data['errors']}")
                    return False
                
                submissions = data.get('data', {}).get('recentSubmissions', [])
                if submissions:
                    print(f"✅ 成功获取 {len(submissions)} 条提交记录")
                    return True
                else:
                    print("⚠️ 没有找到提交记录")
                    return True
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def test_submission_calendar(self, username: str):
        """测试获取提交日历"""
        print(f"\n测试获取用户 {username} 的提交日历...")
        
        query = """
        query userProfileCalendar($username: String!, $year: Int) {
            matchedUser(username: $username) {
                userCalendar(year: $year) {
                    activeYears
                    streak
                    totalActiveDays
                    submissionCalendar
                }
            }
        }
        """
        
        current_year = datetime.now().year
        variables = {"username": username, "year": current_year}
        payload = {
            "query": query,
            "variables": variables,
            "operationName": "userProfileCalendar"
        }
        
        try:
            response = self.session.post(
                self.graphql_url,
                json=payload,
                timeout=15
            )
            
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("响应数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                
                if 'errors' in data:
                    print(f"❌ GraphQL错误: {data['errors']}")
                    return False
                
                user_data = data.get('data', {}).get('matchedUser')
                if user_data and user_data.get('userCalendar'):
                    print("✅ 提交日历获取成功")
                    return True
                else:
                    print("❌ 用户日历数据为空")
                    return False
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def run_all_tests(self, username: str):
        """运行所有测试"""
        print("=" * 60)
        print(f"开始测试力扣API - 用户: {username}")
        print("=" * 60)
        
        results = []
        
        # 测试用户信息
        results.append(self.test_user_profile(username))
        
        # 测试提交记录
        results.append(self.test_recent_submissions(username))
        
        # 测试提交日历
        results.append(self.test_submission_calendar(username))
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        print("=" * 60)
        print(f"用户信息: {'✅ 成功' if results[0] else '❌ 失败'}")
        print(f"提交记录: {'✅ 成功' if results[1] else '❌ 失败'}")
        print(f"提交日历: {'✅ 成功' if results[2] else '❌ 失败'}")
        
        success_count = sum(results)
        print(f"\n总体结果: {success_count}/3 项测试通过")
        
        if success_count == 3:
            print("🎉 所有API测试通过！")
        elif success_count > 0:
            print("⚠️ 部分API可用")
        else:
            print("❌ 所有API测试失败")
        
        return success_count == 3


def main():
    """主函数"""
    if len(sys.argv) > 1:
        username = sys.argv[1]
    else:
        username = input("请输入要测试的力扣用户名: ").strip()
    
    if not username:
        print("用户名不能为空！")
        return
    
    tester = LeetCodeAPITester()
    tester.run_all_tests(username)


if __name__ == "__main__":
    main()
