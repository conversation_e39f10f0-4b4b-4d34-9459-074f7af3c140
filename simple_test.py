#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的力扣API测试
"""

import requests
import json
from datetime import datetime

def test_leetcode_api():
    """测试力扣API"""
    username = "一起消失"
    
    # 基本请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json',
        'Origin': 'https://leetcode.cn',
        'Referer': 'https://leetcode.cn/',
    }
    
    # 测试1: 获取用户基本信息
    print("测试1: 获取用户基本信息")
    print("-" * 40)
    
    query1 = """
    query userPublicProfile($username: String!) {
        userPublicProfile(username: $username) {
            username
            profile {
                realName
                userAvatar
                location
                ranking
            }
        }
    }
    """
    
    payload1 = {
        "query": query1,
        "variables": {"username": username}
    }
    
    try:
        response = requests.post(
            "https://leetcode.cn/graphql/",
            json=payload1,
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"异常: {e}")
    
    print("\n" + "=" * 50 + "\n")
    
    # 测试2: 获取提交日历
    print("测试2: 获取提交日历")
    print("-" * 40)
    
    query2 = """
    query userProfileCalendar($username: String!, $year: Int) {
        matchedUser(username: $username) {
            userCalendar(year: $year) {
                streak
                totalActiveDays
                submissionCalendar
            }
        }
    }
    """
    
    payload2 = {
        "query": query2,
        "variables": {"username": username, "year": datetime.now().year}
    }
    
    try:
        response = requests.post(
            "https://leetcode.cn/graphql/",
            json=payload2,
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
            # 解析今日提交次数
            if data.get('data') and data['data'].get('matchedUser'):
                calendar_data = data['data']['matchedUser'].get('userCalendar')
                if calendar_data and calendar_data.get('submissionCalendar'):
                    submission_calendar = json.loads(calendar_data['submissionCalendar'])
                    today_timestamp = str(int(datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).timestamp()))
                    today_submissions = submission_calendar.get(today_timestamp, 0)
                    print(f"\n今日提交次数: {today_submissions}")
                    print(f"连续天数: {calendar_data.get('streak', 0)}")
                    print(f"总活跃天数: {calendar_data.get('totalActiveDays', 0)}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"异常: {e}")

if __name__ == "__main__":
    test_leetcode_api()
