#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建最终的按分类组织的CSV文件
格式：每列为一个分类名，每行数据为"题号.中文题目"
"""

import pandas as pd
from collections import defaultdict
import csv

def create_final_category_csv():
    """创建最终的按分类组织的CSV文件"""
    
    # 读取原始CSV文件
    try:
        df = pd.read_csv('leetcode_hot100.csv', encoding='utf-8-sig')
        print(f"成功读取CSV文件，共 {len(df)} 道题目")
    except FileNotFoundError:
        print("错误：找不到 leetcode_hot100.csv 文件")
        return
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return
    
    # 按分类分组
    categories = defaultdict(list)
    
    for _, row in df.iterrows():
        category = row['分类']
        question_id = row['题号']
        title_cn = row['题目(中文)']
        
        # 格式化为 "题号.中文题目"
        formatted_item = f"{question_id}.{title_cn}"
        categories[category].append(formatted_item)
    
    # 获取所有分类并排序（按题目数量降序）
    category_names = sorted(categories.keys(), key=lambda x: len(categories[x]), reverse=True)
    print(f"发现 {len(category_names)} 个分类")
    
    # 显示每个分类的题目数量
    print("\n=== 各分类题目数量 ===")
    for category in category_names:
        count = len(categories[category])
        print(f"{category}: {count} 题")
    
    # 找出最长的分类（用于确定行数）
    max_length = max(len(questions) for questions in categories.values())
    print(f"\n最大分类包含 {max_length} 道题目")
    
    # 创建最终的CSV文件
    output_filename = 'leetcode_hot100_final_by_category.csv'
    
    with open(output_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        
        # 写入表头（分类名）
        writer.writerow(category_names)
        
        # 逐行写入数据
        for i in range(max_length):
            row = []
            for category in category_names:
                if i < len(categories[category]):
                    row.append(categories[category][i])
                else:
                    row.append("")  # 空位
            writer.writerow(row)
    
    print(f"\n最终文件已保存到 {output_filename}")
    
    # 读取并显示预览
    final_df = pd.read_csv(output_filename, encoding='utf-8-sig')
    print("\n=== 最终文件预览 ===")
    print("列名（分类）:")
    for i, col in enumerate(final_df.columns, 1):
        print(f"{i:2d}. {col}")
    
    print(f"\n前10行数据:")
    print(final_df.head(10).to_string(index=False))
    
    # 创建一个更紧凑的版本（去除空行）
    create_compact_no_empty_rows(categories, category_names)
    
    return final_df

def create_compact_no_empty_rows(categories, category_names):
    """创建紧凑版本，每列只包含该分类的题目，不用空行对齐"""
    
    output_filename = 'leetcode_hot100_compact_by_category.csv'
    
    # 找出最长的分类
    max_length = max(len(questions) for questions in categories.values())
    
    # 创建数据字典
    data_dict = {}
    for category in category_names:
        questions = categories[category][:]  # 复制列表
        # 补齐到最大长度
        while len(questions) < max_length:
            questions.append("")
        data_dict[category] = questions
    
    # 创建DataFrame并保存
    df = pd.DataFrame(data_dict)
    df.to_csv(output_filename, index=False, encoding='utf-8-sig')
    
    print(f"紧凑版本已保存到 {output_filename}")
    
    # 创建一个真正紧凑的版本（每列独立，不对齐）
    create_truly_compact_version(categories, category_names)

def create_truly_compact_version(categories, category_names):
    """创建真正紧凑的版本，每列独立，长度不同"""
    
    output_filename = 'leetcode_hot100_independent_columns.csv'
    
    # 找出最长的分类长度
    max_length = max(len(questions) for questions in categories.values())
    
    with open(output_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        
        # 写入表头
        writer.writerow(category_names)
        
        # 逐行写入，每列独立
        for i in range(max_length):
            row = []
            for category in category_names:
                if i < len(categories[category]):
                    row.append(categories[category][i])
                else:
                    row.append("")  # 该分类已结束，填空
            
            # 检查这一行是否全为空
            if any(cell.strip() for cell in row):
                writer.writerow(row)
    
    print(f"独立列版本已保存到 {output_filename}")

def create_markdown_table():
    """创建Markdown表格版本"""
    try:
        df = pd.read_csv('leetcode_hot100.csv', encoding='utf-8-sig')
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return
    
    # 按分类分组
    categories = defaultdict(list)
    
    for _, row in df.iterrows():
        category = row['分类']
        question_id = row['题号']
        title_cn = row['题目(中文)']
        
        formatted_item = f"{question_id}.{title_cn}"
        categories[category].append(formatted_item)
    
    # 按题目数量排序
    category_names = sorted(categories.keys(), key=lambda x: len(categories[x]), reverse=True)
    
    # 创建Markdown文件
    output_filename = 'leetcode_hot100_category_table.md'
    
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write("# LeetCode 热题 100 - 按分类组织\n\n")
        
        # 创建表格头
        f.write("| " + " | ".join(category_names) + " |\n")
        f.write("| " + " | ".join(["---"] * len(category_names)) + " |\n")
        
        # 找出最长的分类
        max_length = max(len(questions) for questions in categories.values())
        
        # 逐行写入数据
        for i in range(max_length):
            row = []
            for category in category_names:
                if i < len(categories[category]):
                    row.append(categories[category][i])
                else:
                    row.append("")
            
            # 只写入非空行
            if any(cell.strip() for cell in row):
                f.write("| " + " | ".join(row) + " |\n")
    
    print(f"Markdown表格已保存到 {output_filename}")

def main():
    """主函数"""
    print("创建最终的按分类组织的CSV文件...")
    
    # 创建主要文件
    final_df = create_final_category_csv()
    
    if final_df is not None:
        # 创建Markdown版本
        create_markdown_table()
        
        print("\n=== 处理完成 ===")
        print("生成的文件:")
        print("1. leetcode_hot100_final_by_category.csv - 最终按分类组织的CSV文件")
        print("2. leetcode_hot100_compact_by_category.csv - 紧凑版本")
        print("3. leetcode_hot100_independent_columns.csv - 独立列版本")
        print("4. leetcode_hot100_category_table.md - Markdown表格版本")

if __name__ == "__main__":
    main()
